# Generated by Django 4.2.17 on 2025-07-04 04:41

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='StudentProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_id', models.Char<PERSON>ield(max_length=20, unique=True)),
                ('year', models.Char<PERSON>ield(choices=[('freshman', 'Freshman'), ('sophomore', 'Sophomore'), ('junior', 'Junior'), ('senior', 'Senior'), ('graduate', 'Graduate')], max_length=20)),
                ('interests', models.TextField(default='[]', help_text='JSON list of academic interests/topics')),
                ('career_goals', models.TextField(blank=True)),
                ('preferred_difficulty', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='intermediate', max_length=20)),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='profile_pictures/')),
                ('major', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='course_matcher.department')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('credits', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(6)])),
                ('description', models.TextField()),
                ('difficulty', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='intermediate', max_length=20)),
                ('topics', models.TextField(default='[]', help_text='JSON list of course topics/keywords for content-based matching')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.department')),
                ('prerequisites', models.ManyToManyField(blank=True, to='course_matcher.course')),
            ],
        ),
        migrations.CreateModel(
            name='AdvisingSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField()),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='scheduled', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('advisor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='advising_sessions', to=settings.AUTH_USER_MODEL)),
                ('course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='course_matcher.course')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.studentprofile')),
            ],
        ),
        migrations.CreateModel(
            name='Recommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('confidence_score', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('recommendation_type', models.CharField(choices=[('classification', 'Classification-based'), ('knowledge', 'Knowledge-based'), ('content', 'Content-based'), ('hybrid', 'Hybrid')], max_length=20)),
                ('reasoning', models.TextField(help_text='Explanation for why this course was recommended')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_dismissed', models.BooleanField(default=False)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.course')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.studentprofile')),
            ],
            options={
                'ordering': ['-confidence_score', '-created_at'],
                'unique_together': {('student', 'course')},
            },
        ),
        migrations.CreateModel(
            name='AcademicRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('semester', models.CharField(choices=[('fall', 'Fall'), ('spring', 'Spring'), ('summer', 'Summer')], max_length=20)),
                ('year', models.IntegerField()),
                ('grade', models.CharField(blank=True, choices=[('A', 'A'), ('A-', 'A-'), ('B+', 'B+'), ('B', 'B'), ('B-', 'B-'), ('C+', 'C+'), ('C', 'C'), ('C-', 'C-'), ('D+', 'D+'), ('D', 'D'), ('F', 'F')], max_length=2, null=True)),
                ('date_enrolled', models.DateTimeField(auto_now_add=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.course')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.studentprofile')),
            ],
            options={
                'unique_together': {('student', 'course')},
            },
        ),
    ]
