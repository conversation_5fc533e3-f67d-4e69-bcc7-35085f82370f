<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Test - CourseRec Design System</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        /* Inline CSS for testing - copied from styles.css */

        /* ===== DESIGN TOKENS ===== */
        :root {
            /* Primary Colors */
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-200: #bfdbfe;
            --primary-300: #93c5fd;
            --primary-400: #60a5fa;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-800: #1e40af;
            --primary-900: #1e3a8a;

            /* Gray Scale */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Transitions */
            --transition-fast: 150ms ease-in-out;
            --transition-normal: 300ms ease-in-out;
            --transition-slow: 500ms ease-in-out;
        }

        /* ===== UTILITY CLASSES ===== */
        [x-cloak] {
            display: none !important;
        }

        /* ===== COMPONENT STYLES ===== */
        .card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            transition: all var(--transition-normal);
        }

        .card-hover:hover {
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            transform: translateY(-1px);
        }

        .card-header {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-footer {
            padding: 1rem 1.5rem 1.5rem 1.5rem;
            border-top: 1px solid #e5e7eb;
            background-color: #f9fafb;
        }

        /* Button Components */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 0.375rem;
            transition: all var(--transition-normal);
            cursor: pointer;
            border: none;
            text-decoration: none;
        }

        .btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .btn-primary {
            background-color: var(--primary-600);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-700);
        }

        .btn-secondary {
            background-color: var(--gray-200);
            color: var(--gray-900);
        }

        .btn-secondary:hover {
            background-color: var(--gray-300);
        }

        .btn-outline {
            border: 1px solid var(--gray-300);
            background-color: white;
            color: var(--gray-700);
        }

        .btn-outline:hover {
            background-color: var(--gray-50);
        }

        .btn-success {
            background-color: #10b981;
            color: white;
        }

        .btn-success:hover {
            background-color: #059669;
        }

        .btn-warning {
            background-color: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background-color: #d97706;
        }

        .btn-danger {
            background-color: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background-color: #dc2626;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
        }

        /* Form Components */
        .form-input {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--gray-300);
            border-radius: 0.375rem;
            box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            transition: all var(--transition-normal);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--gray-300);
            border-radius: 0.375rem;
            box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            background-color: white;
        }

        .form-checkbox {
            border-radius: 0.25rem;
            border: 1px solid var(--gray-300);
            color: var(--primary-600);
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        /* Badge Components */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.625rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-primary {
            background-color: var(--primary-100);
            color: var(--primary-800);
        }

        .badge-success {
            background-color: #d1fae5;
            color: #047857;
        }

        .badge-warning {
            background-color: #fef3c7;
            color: #d97706;
        }

        .badge-danger {
            background-color: #fee2e2;
            color: #dc2626;
        }

        .badge-gray {
            background-color: var(--gray-100);
            color: var(--gray-800);
        }

        /* Stats Cards */
        .stat-card-gradient {
            background: linear-gradient(135deg, white, #eff6ff);
            padding: 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            transition: all var(--transition-normal);
        }

        .stat-card-gradient:hover {
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            transform: translateY(-1px);
        }

        .stat-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.06);
        }

        .stat-icon-primary {
            background-color: var(--primary-100);
            color: var(--primary-600);
        }

        .stat-icon-success {
            background-color: #d1fae5;
            color: #059669;
        }

        .stat-icon-warning {
            background-color: #fef3c7;
            color: #d97706;
        }

        .stat-icon-danger {
            background-color: #fee2e2;
            color: #dc2626;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
        }

        .stat-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--gray-500);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Grid Layouts */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(1, minmax(0, 1fr));
            gap: 1.5rem;
        }

        @media (min-width: 768px) {
            .dashboard-grid {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }

        @media (min-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: repeat(4, minmax(0, 1fr));
            }
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(1, minmax(0, 1fr));
            gap: 1.5rem;
        }

        @media (min-width: 768px) {
            .content-grid {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }

        @media (min-width: 1024px) {
            .content-grid {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }
        }

        /* Typography */
        .heading-primary {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--gray-900);
        }

        .heading-secondary {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        .heading-tertiary {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-700);
        }

        .text-muted {
            color: var(--gray-600);
        }

        /* Interactive Elements */
        .card-interactive {
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .card-interactive:hover {
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            transform: translateY(-2px);
        }

        /* Form Groups */
        .form-group {
            margin-bottom: 1.5rem;
        }

        /* Mobile Optimizations */
        @media (max-width: 768px) {
            .stat-card-gradient {
                padding: 1rem;
            }

            .stat-value {
                font-size: 1.25rem;
            }

            .heading-primary {
                font-size: 1.5rem;
            }

            .heading-secondary {
                font-size: 1.25rem;
            }

            .btn {
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
            }

            .card-body {
                padding: 1rem;
            }

            .dashboard-grid {
                grid-template-columns: repeat(1, minmax(0, 1fr));
                gap: 1rem;
            }

            .content-grid {
                grid-template-columns: repeat(1, minmax(0, 1fr));
                gap: 1rem;
            }
        }
    </style>
</head>
<body class="bg-gray-100" x-data="{ isSidebarOpen: true, isMobileMenuOpen: false }">
    
    <!-- Test Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 p-4 mb-8">
        <h1 class="heading-primary">CourseRec Design System Test</h1>
        <p class="text-muted">Testing the unified design components</p>
    </div>
    
    <!-- Test Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Stats Cards Test -->
        <div class="mb-8">
            <h2 class="heading-secondary mb-4">Stats Cards</h2>
            <div class="dashboard-grid">
                <div class="stat-card-gradient">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon stat-icon-primary">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="stat-label">Total Students</dt>
                                <dd class="stat-value">1,234</dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card-gradient">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon stat-icon-success">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="stat-label">Total Courses</dt>
                                <dd class="stat-value">567</dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card-gradient">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon stat-icon-warning">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="stat-label">Active Sessions</dt>
                                <dd class="stat-value">89</dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card-gradient">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon stat-icon-danger">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="stat-label">Pending Reports</dt>
                                <dd class="stat-value">12</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Buttons Test -->
        <div class="mb-8">
            <h2 class="heading-secondary mb-4">Buttons</h2>
            <div class="flex flex-wrap gap-4">
                <button type="button" class="btn btn-primary">Primary Button</button>
                <button type="button" class="btn btn-secondary">Secondary Button</button>
                <button type="button" class="btn btn-outline">Outline Button</button>
                <button type="button" class="btn btn-success">Success Button</button>
                <button type="button" class="btn btn-warning">Warning Button</button>
                <button type="button" class="btn btn-danger">Danger Button</button>
            </div>
        </div>
        
        <!-- Cards Test -->
        <div class="mb-8">
            <h2 class="heading-secondary mb-4">Cards</h2>
            <div class="content-grid">
                <div class="card card-hover">
                    <div class="card-header">
                        <h3 class="heading-tertiary">Card Title</h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">This is a test card with hover effects and proper spacing.</p>
                    </div>
                    <div class="card-footer">
                        <button type="button" class="btn btn-primary btn-sm">Action</button>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <h3 class="heading-tertiary mb-4">Simple Card</h3>
                        <p class="text-muted">A simple card without header or footer.</p>
                    </div>
                </div>
                
                <div class="card card-interactive">
                    <div class="card-body">
                        <h3 class="heading-tertiary mb-4">Interactive Card</h3>
                        <p class="text-muted">This card has interactive hover effects.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Form Test -->
        <div class="mb-8">
            <h2 class="heading-secondary mb-4">Forms</h2>
            <div class="card">
                <div class="card-body">
                    <form class="space-y-6">
                        <div class="form-group">
                            <label for="test-input" class="form-label">Test Input</label>
                            <input type="text" id="test-input" class="form-input" placeholder="Enter some text">
                        </div>
                        
                        <div class="form-group">
                            <label for="test-select" class="form-label">Test Select</label>
                            <select id="test-select" class="form-select">
                                <option>Option 1</option>
                                <option>Option 2</option>
                                <option>Option 3</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-700">Test checkbox</span>
                            </label>
                        </div>
                        
                        <div class="flex justify-end space-x-4">
                            <button type="button" class="btn btn-secondary">Cancel</button>
                            <button type="submit" class="btn btn-primary">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Badges Test -->
        <div class="mb-8">
            <h2 class="heading-secondary mb-4">Badges</h2>
            <div class="flex flex-wrap gap-2">
                <span class="badge badge-primary">Primary</span>
                <span class="badge badge-success">Success</span>
                <span class="badge badge-warning">Warning</span>
                <span class="badge badge-danger">Danger</span>
                <span class="badge badge-gray">Gray</span>
            </div>
        </div>
        
    </div>
    
</body>
</html>
