{% extends 'base.html' %}
{% load static %}

{% block sidebar %}

<!-- Mobile Sidebar -->
<div
    x-show="isMobileMenuOpen"
    x-transition:enter="transition-transform duration-300 ease-in-out"
    x-transition:enter-start="-translate-x-full"
    x-transition:enter-end="translate-x-0"
    x-transition:leave="transition-transform duration-300 ease-in-out"
    x-transition:leave-start="translate-x-0"
    x-transition:leave-end="-translate-x-full"
    class="mobile-nav-panel lg:hidden"
    @keydown.escape.window="isMobileMenuOpen = false"
    x-cloak
>
    <!-- Mobile Sidebar Header -->
    <div class="flex h-16 flex-shrink-0 items-center justify-between px-4">
        <a href="{% url 'home' %}" class="flex items-center">
            <div class="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>
            </div>
            <span class="ml-2 text-white font-bold text-xl">Student Portal</span>
        </a>
        <button @click="isMobileMenuOpen = false"
                class="mobile-nav-toggle"
                aria-label="Close sidebar">
            <span class="sr-only">Close sidebar</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
        </button>
    </div>
    <!-- Mobile Nav Links -->
    <nav class="flex-1 overflow-y-auto mt-8 px-2 space-y-2">
        <a href="{% url 'home' %}" up-follow class="nav-item" @click="isMobileMenuOpen = false">
            <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20"><path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path><path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path></svg>
            <span class="ml-3">Dashboard</span>
        </a>

        <a href="{% url 'academic_records' %}" up-follow class="nav-item" @click="isMobileMenuOpen = false">
            <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
            <span class="ml-3">Academic Records</span>
        </a>

        <a href="{% url 'student_interests' %}" up-follow class="nav-item" @click="isMobileMenuOpen = false">
            <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path></svg>
            <span class="ml-3">Interests</span>
        </a>

        <a href="{% url 'career_goals' %}" up-follow class="nav-item" @click="isMobileMenuOpen = false">
            <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path><path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path></svg>
            <span class="ml-3">Career Goals</span>
        </a>
        <a href="{% url 'recommendations' %}" up-follow class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-700 group transition-colors">
            <svg class="flex-shrink-0 w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
            <span class="ml-3">Recommendations</span>
        </a>

        <!-- User Menu for Mobile -->
        <div class="pt-4 mt-4 border-t border-gray-700">
            {% if user.is_authenticated %}
                <div class="px-2 mb-2 flex items-center space-x-3">
                    <!-- Profile Picture -->
                    <div class="avatar-container">
                        {% if user.profile.profile_picture %}
                            <img src="{{ user.profile.profile_picture.url }}"
                                 alt="{{ user.get_full_name|default:user.username }}"
                                 class="avatar avatar-sm">
                        {% else %}
                            <img src="{% static 'images/avatars/default-avatar.svg' %}"
                                 alt="{{ user.get_full_name|default:user.username }}"
                                 class="avatar avatar-sm">
                        {% endif %}
                    </div>
                    <div>
                        <p class="text-sm font-semibold text-white">{{ user.get_full_name|default:user.username }}</p>
                        {% if user.email %}<p class="text-xs text-gray-400">{{ user.email }}</p>{% endif %}
                    </div>
                </div>
                <a href="{% url 'logout' %}" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-700 group transition-colors">
                    <svg class="flex-shrink-0 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path></svg>
                    <span class="ml-3">Logout</span>
                </a>
            {% else %}
                <a href="{% url 'login' %}" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-700 group transition-colors">
                    <svg class="flex-shrink-0 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path></svg>
                    <span class="ml-3">Login</span>
                </a>
            {% endif %}
        </div>
    </nav>
</div>

<!-- Desktop Sidebar -->
<aside
    class="hidden lg:flex lg:flex-col nav-sidebar"
    :class="isSidebarOpen ? 'nav-sidebar-expanded' : 'nav-sidebar-collapsed'"
>
    <!-- Desktop Sidebar Header -->
    <div class="flex h-16 flex-shrink-0 items-center" :class="isSidebarOpen ? 'justify-between px-4' : 'justify-center px-2'">
        <a href="{% url 'home' %}" class="flex items-center" x-show="isSidebarOpen" x-transition:opacity x-cloak>
            <div class="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>
            </div>
            <span class="ml-2 text-white font-bold text-xl">Student Portal</span>
        </a>
        <button @click="isSidebarOpen = !isSidebarOpen"
                class="mobile-nav-toggle"
                :class="isSidebarOpen ? 'p-2' : 'p-1'"
                aria-label="Toggle sidebar">
            <svg x-show="isSidebarOpen" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" x-cloak><path stroke-linecap="round" stroke-linejoin="round" d="M18.75 19.5l-7.5-7.5 7.5-7.5m-6 15L5.25 12l7.5-7.5" /></svg>
            <svg x-show="!isSidebarOpen" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" x-cloak><path stroke-linecap="round" stroke-linejoin="round" d="M5.25 4.5l7.5 7.5-7.5 7.5m6-15l7.5 7.5-7.5 7.5" /></svg>
        </button>
    </div>
    <!-- Desktop Nav Links -->
    <nav class="flex-1 overflow-y-auto mt-8 px-2 space-y-2">
        <div class="relative group">
            <a href="{% url 'home' %}" up-follow class="nav-item">
                <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20"><path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path><path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path></svg>
                <span class="ml-3 flex-1 whitespace-nowrap" x-show="isSidebarOpen">Dashboard</span>
            </a>
            <div x-show="!isSidebarOpen" class="nav-tooltip" x-cloak>Dashboard</div>
        </div>

        <div class="relative group">
            <a href="{% url 'academic_records' %}" up-follow class="nav-item">
                <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                <span class="ml-3 flex-1 whitespace-nowrap" x-show="isSidebarOpen">Academic Records</span>
            </a>
            <div x-show="!isSidebarOpen" class="nav-tooltip" x-cloak>Academic Records</div>
        </div>

        <div class="relative group">
            <a href="{% url 'student_interests' %}" up-follow class="nav-item">
                <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path></svg>
                <span class="ml-3 flex-1 whitespace-nowrap" x-show="isSidebarOpen">Interests</span>
            </a>
            <div x-show="!isSidebarOpen" class="nav-tooltip" x-cloak>Interests</div>
        </div>

        <div class="relative group">
            <a href="{% url 'career_goals' %}" up-follow class="nav-item">
                <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path><path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path></svg>
                <span class="ml-3 flex-1 whitespace-nowrap" x-show="isSidebarOpen">Career Goals</span>
            </a>
            <div x-show="!isSidebarOpen" class="nav-tooltip" x-cloak>Career Goals</div>
        </div>

        <div class="relative group">
            <a href="{% url 'recommendations' %}" up-follow class="nav-item">
                <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                <span class="ml-3 flex-1 whitespace-nowrap" x-show="isSidebarOpen">Recommendations</span>
            </a>
            <div x-show="!isSidebarOpen" class="nav-tooltip" x-cloak>Recommendations</div>
        </div>
    </nav>
</aside>
{% endblock %}

{% block main_classes %}flex-1 bg-gray-100 p-6 transition-all duration-300{% endblock %}

{% block main_dynamic_classes %}
:class="isSidebarOpen ? 'lg:ml-64' : 'lg:ml-20'"
{% endblock %}