/* Unified Design System for CourseRec Application */

/* ===== DESIGN TOKENS ===== */
:root {
    /* Primary Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Gray Scale */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Semantic Colors */
    --success-50: #ecfdf5;
    --success-100: #d1fae5;
    --success-500: #10b981;
    --success-600: #059669;
    --success-700: #047857;

    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-500: #f59e0b;
    --warning-600: #d97706;

    --error-50: #fef2f2;
    --error-100: #fee2e2;
    --error-500: #ef4444;
    --error-600: #dc2626;

    /* Typography */
    --font-family-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* ===== UTILITY CLASSES ===== */

/* Hide elements with x-cloak */
[x-cloak] {
    display: none !important;
}

/* HTMX Loading States */
.htmx-indicator {
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.htmx-request .htmx-indicator {
    opacity: 1;
}

/* Focus States for Accessibility */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* ===== COMPONENT STYLES ===== */

/* Unified Card Component */
.card {
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.card-hover:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-0.25rem);
}

.card-gradient {
    background: linear-gradient(135deg, white, var(--primary-50));
}

.card-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-50);
}

/* Unified Button Components */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    cursor: pointer;
    border: none;
    text-decoration: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px var(--primary-500);
}

.btn-primary {
    background-color: var(--primary-600);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-700);
}

.btn-secondary {
    background-color: var(--gray-200);
    color: var(--gray-900);
}

.btn-secondary:hover {
    background-color: var(--gray-300);
}

.btn-outline {
    border: 1px solid var(--gray-300);
    background-color: white;
    color: var(--gray-700);
}

.btn-outline:hover {
    background-color: var(--gray-50);
}

.btn-success {
    background-color: var(--success-600);
    color: white;
}

.btn-success:hover {
    background-color: var(--success-700);
}

.btn-warning {
    background-color: var(--warning-500);
    color: white;
}

.btn-warning:hover {
    background-color: var(--warning-600);
}

.btn-danger {
    background-color: var(--error-500);
    color: white;
}

.btn-danger:hover {
    background-color: var(--error-600);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Form Components */
.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.form-input::placeholder {
    color: var(--gray-400);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    background-color: white;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-checkbox {
    border-radius: var(--radius-sm);
    border: 1px solid var(--gray-300);
    color: var(--primary-600);
    box-shadow: var(--shadow-sm);
}

.form-checkbox:focus {
    border-color: var(--primary-300);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-error {
    font-size: 0.875rem;
    color: var(--error-600);
    margin-top: 0.25rem;
}

/* Badge Components */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.625rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-primary {
    background-color: var(--primary-100);
    color: var(--primary-800);
}

.badge-success {
    background-color: var(--success-100);
    color: var(--success-700);
}

.badge-warning {
    background-color: var(--warning-100);
    color: var(--warning-600);
}

.badge-danger {
    background-color: var(--error-100);
    color: var(--error-600);
}

.badge-gray {
    background-color: var(--gray-100);
    color: var(--gray-800);
}

/* Navigation Components */
.nav-sidebar {
    @apply fixed inset-y-0 left-0 z-40 bg-gray-800 text-white transition-all duration-300;
}

.nav-sidebar-collapsed {
    @apply w-20;
}

.nav-sidebar-expanded {
    @apply w-64;
}

.nav-item {
    @apply flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-700 group transition-colors duration-200;
}

.nav-item-active {
    @apply bg-gray-700 text-white;
}

.nav-icon {
    @apply flex-shrink-0 w-5 h-5 text-gray-400 transition duration-75 group-hover:text-white;
}

.nav-tooltip {
    @apply absolute left-full ml-2 p-2 bg-gray-900 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50;
}

/* Mobile Navigation */
.mobile-nav-overlay {
    @apply fixed inset-0 z-40 bg-gray-600 bg-opacity-75 transition-opacity;
}

.mobile-nav-panel {
    @apply relative flex-1 flex flex-col max-w-xs w-full bg-gray-800;
}

.mobile-nav-toggle {
    @apply inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-white transition-all duration-200;
}

/* Stats/Metrics Cards */
.stat-card {
    @apply bg-white p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg;
}

.stat-card-gradient {
    @apply bg-gradient-to-br from-white to-blue-50 p-6 rounded-lg shadow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
}

.stat-icon {
    @apply w-10 h-10 rounded-full flex items-center justify-center shadow-inner;
}

.stat-icon-primary {
    @apply bg-blue-100 text-blue-600;
}

.stat-icon-success {
    @apply bg-green-100 text-green-600;
}

.stat-icon-warning {
    @apply bg-yellow-100 text-yellow-600;
}

.stat-icon-danger {
    @apply bg-red-100 text-red-600;
}

.stat-value {
    @apply text-2xl font-bold text-gray-900;
}

.stat-label {
    @apply text-sm font-medium text-gray-500 truncate;
}

/* Grid Layouts */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .dashboard-grid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .content-grid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .content-grid {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

/* Typography Utilities */
.heading-primary {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-900);
    line-height: 1.2;
}

.heading-secondary {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    line-height: 1.3;
}

.heading-tertiary {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-700);
    line-height: 1.4;
}

.text-muted {
    color: var(--gray-600);
}

.text-subtle {
    color: var(--gray-500);
}

/* Loading States */
.loading-spinner {
    @apply animate-spin h-5 w-5 text-white;
}

.loading-overlay {
    @apply fixed top-4 right-4 z-50 bg-primary-600 text-white px-4 py-2 rounded-md shadow-lg flex items-center;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }

    .mobile-full-width {
        width: 100% !important;
    }

    .mobile-stack {
        flex-direction: column !important;
    }
}

@media (min-width: 1024px) {
    .desktop-only {
        display: block !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus visible for better keyboard navigation */
.focus-visible:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 1px solid var(--gray-300);
    }

    .btn {
        border: 1px solid currentColor;
    }
}

/* Enhanced Interactive States */
.interactive-element {
    transition: all var(--transition-normal);
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.interactive-element:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

/* Loading States */
.htmx-loading {
    position: relative;
    pointer-events: none;
}

.htmx-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.htmx-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-200);
    border-top: 2px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Form Styles */
.form-group {
    @apply mb-6;
}

.form-input:focus {
    @apply ring-2 ring-primary-500 ring-opacity-50 border-primary-500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:invalid {
    @apply border-red-500;
}

.form-input:invalid:focus {
    @apply ring-red-500 border-red-500;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Enhanced Button States */
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.btn:disabled {
    @apply opacity-50 cursor-not-allowed;
    pointer-events: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-600));
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--gray-300), var(--gray-200));
}

/* Card Enhancements */
.card-interactive {
    @apply cursor-pointer transition-all duration-300;
}

.card-interactive:hover {
    @apply shadow-xl;
    transform: translateY(-2px);
}

.card-interactive:active {
    transform: translateY(0);
}

/* Navigation Enhancements */
.nav-item:hover {
    background: linear-gradient(135deg, rgba(55, 65, 81, 0.8), rgba(75, 85, 99, 0.6));
}

.nav-item:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.nav-item-active {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-600));
    color: white;
}

.nav-item-active .nav-icon {
    color: white;
}

/* Tooltip Enhancements */
.nav-tooltip {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    box-shadow: var(--shadow-lg);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .stat-card-gradient {
        @apply p-4;
    }

    .stat-value {
        @apply text-xl;
    }

    .heading-primary {
        @apply text-2xl;
    }

    .heading-secondary {
        @apply text-xl;
    }

    .btn {
        @apply px-3 py-2 text-sm;
    }

    .card-body {
        @apply p-4;
    }

    .dashboard-grid {
        @apply grid-cols-1 gap-4;
    }

    .content-grid {
        @apply grid-cols-1 gap-4;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn {
        @apply py-3 px-4 text-base;
        min-height: 44px;
    }

    .nav-item {
        @apply py-3;
        min-height: 44px;
    }

    .mobile-nav-toggle {
        @apply p-3;
        min-width: 44px;
        min-height: 44px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .card-hover:hover {
        transform: none;
    }

    .stat-card-gradient:hover {
        transform: none;
    }
}

/* Print Styles */
@media print {
    .nav-sidebar,
    .mobile-nav-panel,
    .mobile-nav-toggle,
    .btn,
    .htmx-indicator {
        display: none !important;
    }

    .card {
        @apply border border-gray-300;
        box-shadow: none;
    }

    .heading-primary,
    .heading-secondary,
    .heading-tertiary {
        color: black !important;
    }
}
