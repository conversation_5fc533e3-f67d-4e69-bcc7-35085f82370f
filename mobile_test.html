<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Responsiveness Test - CourseRec</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <style>
        /* Mobile-first responsive design test */
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }
        
        .responsive-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        @media (min-width: 640px) {
            .responsive-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (min-width: 1024px) {
            .responsive-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        
        .test-card {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        }
        
        .mobile-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: white;
            border-bottom: 1px solid #e5e7eb;
            z-index: 50;
            padding: 1rem;
        }
        
        .nav-toggle {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem;
            border-radius: 0.375rem;
            background: #f3f4f6;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .nav-toggle:hover {
            background: #e5e7eb;
        }
        
        .nav-toggle:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            width: 16rem;
            background: #1f2937;
            color: white;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            z-index: 40;
        }
        
        .sidebar.open {
            transform: translateX(0);
        }
        
        .sidebar-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 30;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .sidebar-overlay.open {
            opacity: 1;
            visibility: visible;
        }
        
        @media (min-width: 1024px) {
            .sidebar {
                position: relative;
                transform: translateX(0);
                width: 16rem;
            }
            
            .sidebar-overlay {
                display: none;
            }
            
            .nav-toggle {
                display: none;
            }
        }
        
        .main-content {
            margin-top: 5rem;
            transition: margin-left 0.3s ease;
        }
        
        @media (min-width: 1024px) {
            .main-content {
                margin-left: 16rem;
                margin-top: 0;
            }
        }
        
        /* Touch-friendly buttons */
        .touch-button {
            min-height: 44px;
            min-width: 44px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        
        @media (hover: none) and (pointer: coarse) {
            .test-card {
                padding: 1rem;
            }
            
            .touch-button {
                min-height: 48px;
                padding: 1rem 1.25rem;
            }
        }
    </style>
</head>
<body class="bg-gray-100" x-data="{ sidebarOpen: false }">
    
    <!-- Mobile Navigation -->
    <nav class="mobile-nav lg:hidden">
        <div class="flex items-center justify-between">
            <h1 class="text-xl font-bold text-gray-900">CourseRec</h1>
            <button @click="sidebarOpen = !sidebarOpen" 
                    class="nav-toggle"
                    :aria-expanded="sidebarOpen"
                    aria-label="Toggle navigation">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path x-show="!sidebarOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    <path x-show="sidebarOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </nav>
    
    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" 
         :class="{ 'open': sidebarOpen }"
         @click="sidebarOpen = false"
         x-show="sidebarOpen"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"></div>
    
    <!-- Sidebar -->
    <aside class="sidebar" :class="{ 'open': sidebarOpen }">
        <div class="p-4">
            <h2 class="text-lg font-semibold mb-4">Navigation</h2>
            <nav class="space-y-2">
                <a href="#" class="block px-3 py-2 rounded-md text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">Dashboard</a>
                <a href="#" class="block px-3 py-2 rounded-md text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">Students</a>
                <a href="#" class="block px-3 py-2 rounded-md text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">Courses</a>
                <a href="#" class="block px-3 py-2 rounded-md text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">Reports</a>
            </nav>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main class="main-content">
        <div class="test-container">
            
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Mobile Responsiveness Test</h1>
                <p class="text-gray-600">Testing responsive design across different screen sizes</p>
            </div>
            
            <!-- Responsive Grid Test -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Responsive Grid</h2>
                <div class="responsive-grid">
                    <div class="test-card">
                        <h3 class="text-lg font-semibold mb-2">Card 1</h3>
                        <p class="text-gray-600">This card should stack on mobile and arrange in a grid on larger screens.</p>
                    </div>
                    <div class="test-card">
                        <h3 class="text-lg font-semibold mb-2">Card 2</h3>
                        <p class="text-gray-600">Test responsive behavior and touch interactions.</p>
                    </div>
                    <div class="test-card">
                        <h3 class="text-lg font-semibold mb-2">Card 3</h3>
                        <p class="text-gray-600">Hover effects should work on desktop, touch on mobile.</p>
                    </div>
                    <div class="test-card">
                        <h3 class="text-lg font-semibold mb-2">Card 4</h3>
                        <p class="text-gray-600">All cards should be easily tappable on mobile devices.</p>
                    </div>
                </div>
            </div>
            
            <!-- Button Test -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Touch-Friendly Buttons</h2>
                <div class="flex flex-wrap gap-4">
                    <button class="touch-button bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">Primary</button>
                    <button class="touch-button bg-gray-200 text-gray-900 rounded-md hover:bg-gray-300 transition-colors">Secondary</button>
                    <button class="touch-button border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 transition-colors">Outline</button>
                </div>
            </div>
            
            <!-- Form Test -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Responsive Form</h2>
                <div class="test-card max-w-md">
                    <form class="space-y-4">
                        <div>
                            <label for="test-input" class="block text-sm font-medium text-gray-700 mb-1">Test Input</label>
                            <input type="text" id="test-input" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Touch-friendly input">
                        </div>
                        <div>
                            <label for="test-select" class="block text-sm font-medium text-gray-700 mb-1">Test Select</label>
                            <select id="test-select" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>Option 1</option>
                                <option>Option 2</option>
                                <option>Option 3</option>
                            </select>
                        </div>
                        <button type="submit" class="touch-button w-full bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">Submit</button>
                    </form>
                </div>
            </div>
            
            <!-- Viewport Info -->
            <div class="test-card">
                <h3 class="text-lg font-semibold mb-2">Viewport Information</h3>
                <p class="text-sm text-gray-600 mb-2">Current viewport width: <span id="viewport-width"></span>px</p>
                <p class="text-sm text-gray-600 mb-2">Device pixel ratio: <span id="device-ratio"></span></p>
                <p class="text-sm text-gray-600">Touch support: <span id="touch-support"></span></p>
            </div>
            
        </div>
    </main>
    
    <script>
        // Update viewport information
        function updateViewportInfo() {
            document.getElementById('viewport-width').textContent = window.innerWidth;
            document.getElementById('device-ratio').textContent = window.devicePixelRatio || 1;
            document.getElementById('touch-support').textContent = 'ontouchstart' in window ? 'Yes' : 'No';
        }
        
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
        
        // Test keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // Close sidebar on escape
                Alpine.store('sidebarOpen', false);
            }
        });
    </script>
    
</body>
</html>
