{% extends 'student/base.html' %}
{% load static %}

{% block title %}Profile Settings - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    
    <!-- Header -->
    <div class="mb-8">
        <h1 class="heading-primary">Profile Settings</h1>
        <p class="mt-2 text-muted">Manage your account details and preferences.</p>
    </div>

    <!-- Profile Form -->
    <div class="card">
        <div class="card-header">
            <h2 class="heading-secondary">Personal Information</h2>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}
                
                <!-- Profile Picture Section -->
                <div class="form-group">
                    <label class="form-label">Profile Picture</label>
                    <div class="flex items-center space-x-6">
                        <!-- Current Avatar -->
                        <div class="avatar-container">
                            {% if user.studentprofile.profile_picture %}
                                <img src="{{ user.studentprofile.profile_picture.url }}" 
                                     alt="{{ user.get_full_name|default:user.username }}"
                                     class="avatar avatar-xl"
                                     id="current-avatar">
                            {% else %}
                                <img src="{% static 'images/avatars/default-avatar.svg' %}" 
                                     alt="{{ user.get_full_name|default:user.username }}"
                                     class="avatar avatar-xl"
                                     id="current-avatar">
                            {% endif %}
                        </div>
                        
                        <!-- Upload Area -->
                        <div class="flex-1">
                            <div class="image-upload-area">
                                <input type="file" 
                                       name="profile_picture" 
                                       id="profile_picture" 
                                       accept="image/*"
                                       class="sr-only">
                                <div class="text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="mt-4">
                                        <p class="text-sm text-gray-600">
                                            <span class="font-medium text-primary-600 hover:text-primary-500">Click to upload</span>
                                            or drag and drop
                                        </p>
                                        <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Full Name -->
                <div class="form-group">
                    <label for="full_name" class="form-label">Full Name</label>
                    <input type="text" 
                           id="full_name" 
                           name="full_name" 
                           value="{{ user.get_full_name }}"
                           class="form-input"
                           placeholder="Enter your full name">
                </div>

                <!-- Email -->
                <div class="form-group">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           value="{{ user.email }}"
                           class="form-input"
                           placeholder="Enter your email address">
                </div>

                <!-- Student ID -->
                <div class="form-group">
                    <label for="student_id" class="form-label">University ID</label>
                    <input type="text" 
                           id="student_id" 
                           name="student_id" 
                           value="{{ user.studentprofile.student_id }}"
                           class="form-input"
                           placeholder="Enter your student ID">
                </div>

                <!-- Department -->
                <div class="form-group">
                    <label for="department" class="form-label">Department</label>
                    <select id="department" name="department" class="form-select">
                        <option value="">Select your department</option>
                        <!-- Department options will be populated here -->
                    </select>
                </div>

                <!-- Year of Study -->
                <div class="form-group">
                    <label for="year" class="form-label">Year of Study</label>
                    <select id="year" name="year" class="form-select">
                        <option value="">Select your year</option>
                        <option value="freshman" {% if user.studentprofile.year == 'freshman' %}selected{% endif %}>Freshman</option>
                        <option value="sophomore" {% if user.studentprofile.year == 'sophomore' %}selected{% endif %}>Sophomore</option>
                        <option value="junior" {% if user.studentprofile.year == 'junior' %}selected{% endif %}>Junior</option>
                        <option value="senior" {% if user.studentprofile.year == 'senior' %}selected{% endif %}>Senior</option>
                        <option value="graduate" {% if user.studentprofile.year == 'graduate' %}selected{% endif %}>Graduate</option>
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 pt-6">
                    <button type="button" class="btn btn-secondary">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Profile</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Security Section -->
    <div class="card mt-8">
        <div class="card-header">
            <h2 class="heading-secondary">Security</h2>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">Re-enroll Facial Data</h3>
                        <p class="text-sm text-gray-500">Update your facial recognition data for exam security</p>
                    </div>
                    <button type="button" class="btn btn-outline">Re-enroll</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle profile picture preview
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('profile_picture');
    const currentAvatar = document.getElementById('current-avatar');
    
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                currentAvatar.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
{% endblock %}
