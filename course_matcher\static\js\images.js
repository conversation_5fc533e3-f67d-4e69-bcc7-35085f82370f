// Image handling functionality for CourseRec

document.addEventListener('DOMContentLoaded', function() {
    initializeImageHandling();
});

function initializeImageHandling() {
    // Initialize lazy loading for images
    initializeLazyLoading();
    
    // Initialize image error handling
    initializeImageErrorHandling();
    
    // Initialize image upload functionality
    initializeImageUpload();
    
    // Initialize avatar hover effects
    initializeAvatarEffects();
}

// Lazy loading implementation
function initializeLazyLoading() {
    const lazyImages = document.querySelectorAll('.lazy-image');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy-image');
                    img.classList.add('loaded');
                    imageObserver.unobserve(img);
                }
            });
        });

        lazyImages.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for browsers without IntersectionObserver
        lazyImages.forEach(img => {
            img.src = img.dataset.src;
            img.classList.remove('lazy-image');
            img.classList.add('loaded');
        });
    }
}

// Image error handling
function initializeImageErrorHandling() {
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
        img.addEventListener('error', function() {
            handleImageError(this);
        });
        
        img.addEventListener('load', function() {
            this.classList.remove('image-loading');
        });
    });
}

function handleImageError(img) {
    // If it's an avatar, show default avatar
    if (img.classList.contains('avatar')) {
        img.src = '/static/images/avatars/default-avatar.svg';
        img.alt = 'Default Avatar';
    } else if (img.classList.contains('logo')) {
        // For logos, show a text fallback
        const textFallback = document.createElement('div');
        textFallback.className = 'logo-fallback';
        textFallback.textContent = img.alt || 'Logo';
        img.parentNode.replaceChild(textFallback, img);
    } else {
        // Generic error handling
        img.classList.add('image-error');
        img.alt = 'Image failed to load';
    }
}

// Image upload functionality
function initializeImageUpload() {
    const uploadAreas = document.querySelectorAll('.image-upload-area');
    
    uploadAreas.forEach(area => {
        const input = area.querySelector('input[type="file"]');
        
        if (input) {
            // Handle drag and drop
            area.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            area.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            area.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0], input);
                }
            });
            
            // Handle click to upload
            area.addEventListener('click', function() {
                input.click();
            });
            
            // Handle file input change
            input.addEventListener('change', function() {
                if (this.files.length > 0) {
                    handleFileUpload(this.files[0], this);
                }
            });
        }
    });
}

function handleFileUpload(file, input) {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        showNotification('Please select a valid image file (JPEG, PNG, GIF, or WebP)', 'error');
        return;
    }
    
    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
        showNotification('Image file size must be less than 5MB', 'error');
        return;
    }
    
    // Show preview
    const reader = new FileReader();
    reader.onload = function(e) {
        showImagePreview(e.target.result, input);
    };
    reader.readAsDataURL(file);
}

function showImagePreview(src, input) {
    const uploadArea = input.closest('.image-upload-area');
    let preview = uploadArea.querySelector('.image-preview');
    
    if (!preview) {
        preview = document.createElement('img');
        preview.className = 'image-preview';
        uploadArea.appendChild(preview);
    }
    
    preview.src = src;
    preview.style.display = 'block';
}

// Avatar effects
function initializeAvatarEffects() {
    const avatars = document.querySelectorAll('.avatar');
    
    avatars.forEach(avatar => {
        // Add loading state
        avatar.addEventListener('loadstart', function() {
            this.classList.add('image-loading');
        });
        
        avatar.addEventListener('load', function() {
            this.classList.remove('image-loading');
        });
        
        // Add hover effects for interactive avatars
        if (avatar.closest('a') || avatar.classList.contains('interactive')) {
            avatar.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
            });
            
            avatar.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        }
    });
}

// Utility function for notifications
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button type="button" class="notification-close">&times;</button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
    
    // Handle close button
    notification.querySelector('.notification-close').addEventListener('click', function() {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    });
}

// Image optimization utilities
function optimizeImage(file, maxWidth = 800, quality = 0.8) {
    return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = function() {
            // Calculate new dimensions
            let { width, height } = img;
            if (width > maxWidth) {
                height = (height * maxWidth) / width;
                width = maxWidth;
            }
            
            // Set canvas size
            canvas.width = width;
            canvas.height = height;
            
            // Draw and compress
            ctx.drawImage(img, 0, 0, width, height);
            canvas.toBlob(resolve, 'image/jpeg', quality);
        };
        
        img.src = URL.createObjectURL(file);
    });
}

// Export functions for use in other scripts
window.ImageHandler = {
    handleImageError,
    showImagePreview,
    optimizeImage,
    showNotification
};
