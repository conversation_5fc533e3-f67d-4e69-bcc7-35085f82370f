/* ===== IMAGE STYLES ===== */

/* Logo Styles */
.logo {
    height: 2rem;
    width: auto;
    transition: all var(--transition-normal);
}

.logo-sm {
    height: 1.5rem;
    width: auto;
}

.logo-lg {
    height: 3rem;
    width: auto;
}

.logo:hover {
    transform: scale(1.05);
}

/* Avatar Styles */
.avatar {
    border-radius: 50%;
    object-fit: cover;
    transition: all var(--transition-normal);
}

.avatar-xs {
    width: 1.5rem;
    height: 1.5rem;
}

.avatar-sm {
    width: 2rem;
    height: 2rem;
}

.avatar-md {
    width: 2.5rem;
    height: 2.5rem;
}

.avatar-lg {
    width: 4rem;
    height: 4rem;
}

.avatar-xl {
    width: 6rem;
    height: 6rem;
}

.avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Avatar with status indicator */
.avatar-container {
    position: relative;
    display: inline-block;
}

.avatar-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    border: 2px solid white;
}

.avatar-status-online {
    background-color: #10b981;
}

.avatar-status-offline {
    background-color: #6b7280;
}

.avatar-status-busy {
    background-color: #ef4444;
}

/* Image Loading States */
.image-loading {
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.image-error {
    background-color: #fee2e2;
    color: #dc2626;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    text-align: center;
}

/* Responsive Images */
.img-responsive {
    max-width: 100%;
    height: auto;
}

.img-cover {
    object-fit: cover;
}

.img-contain {
    object-fit: contain;
}

/* Image Upload Styles */
.image-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: 2rem;
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.image-upload-area:hover {
    border-color: var(--primary-500);
    background-color: var(--primary-50);
}

.image-upload-area.dragover {
    border-color: var(--primary-600);
    background-color: var(--primary-100);
}

.image-preview {
    max-width: 200px;
    max-height: 200px;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
}

/* Lazy Loading */
.lazy-image {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy-image.loaded {
    opacity: 1;
}

/* Image Gallery */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.image-gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.image-gallery-item:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

.image-gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .logo {
        height: 1.75rem;
    }
    
    .avatar-lg {
        width: 3rem;
        height: 3rem;
    }
    
    .avatar-xl {
        width: 4rem;
        height: 4rem;
    }
    
    .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.75rem;
    }
    
    .image-gallery-item img {
        height: 150px;
    }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo,
    .avatar {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .avatar,
    .logo {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}
